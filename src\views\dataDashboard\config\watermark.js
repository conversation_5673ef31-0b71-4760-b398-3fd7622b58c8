/**
 * 水印配置
 * 后期可以通过接口获取姓名和手机号
 */

// 默认水印配置
export const DEFAULT_WATERMARK_CONFIG = {
  // 水印内容（后期通过接口获取）
  content: '张三 13800138000',
  // 水印字体颜色
  fontColor: 'rgba(0, 0, 0, 0.2)',
  // 水印字体大小
  fontSize: 14,
  // 水印之间的间距
  gap: 100,
  // 水印旋转角度
  rotate: -30,
  // 水印层级
  zIndex: 9999,
  // 水印透明度
  opacity: 0.3,
}

/**
 * 获取水印配置
 * 后期可以通过接口获取姓名和手机号
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 水印配置
 */
export const getWatermarkConfig = async (customConfig = {}) => {
  const { name, mobile } = window.G_STORE
  const content = `${name} ${mobile}`
  return {
    ...DEFAULT_WATERMARK_CONFIG,
    ...customConfig,
    content,
  }
}
